import Button from "@/components/Buttons/Button";
import { ItemComparisonArrow } from "@/components/Items/ItemComparisonArrow";
import { displayMissingIcon } from "@/helpers/displayMissingIcon";
import { getItemStatWithUpgrades } from "@/helpers/itemHelpers";
import { rarityColours } from "@/helpers/rarityColours";
import { cn } from "@/lib/utils";
import { Tooltip } from "react-tooltip";
import { useNormalStore } from "../../../app/store/stores";
import useEquipItem from "../api/useEquipItem";

const EquipTooltip = ({ inventory, currentUser, equippedItems }) => {
    const { equipItem } = useEquipItem();
    const { hideItemTooltip, setHideItemTooltip } = useNormalStore();

    const displayStats = (modifiers) => {
        return Object.entries(modifiers).map(([key, value]) => {
            const val = (value - 1) * 100;
            return (
                <p key={key} className="text-base">
                    <span className="text-custom-yellow">+{Math.round(key === "health" ? value : val)}</span>
                    {key !== "health" && "%"}{" "}
                    {key
                        .replace("health", "HP")
                        .replace("strength", "STR")
                        .replace("dexterity", "DEX")
                        .replace("intelligence", "INT")
                        .replace("lifesteal", "LIFE STEAL")
                        .replace("defence", "DEF")
                        .replace("npcDamage", "Increased DMG vs NPCs")
                        .replace("fleeChance", "chance to Flee")
                        .replace("encounterReward", "Yen gained from encounters")}
                </p>
            );
        });
    };

    const getIsEquipped = (item) => {
        const equippedItem = equippedItems?.[item.itemType] || null;
        if (!equippedItem) return false;

        if (item.id === equippedItem.id) return true;
    };

    return (
        <Tooltip
            openOnClick
            clickable
            id="equip-tooltip"
            afterHide={() => hideItemTooltip && setHideItemTooltip(false)}
            className="pointer-events-auto z-600 max-h-[40dvh] overflow-y-auto overflow-x-hidden border border-gray-600/50"
            opacity="1"
            style={{
                backgroundColor: "rgba(7, 6, 7, 0.97)",
                color: "#FFF",
                padding: 0,
            }}
            render={() => (
                <div className="flex h-full w-72 flex-col py-2 text-center xl:w-80">
                    {(!inventory || inventory.length === 0) && (
                        <p className="text-center text-sm">No items available</p>
                    )}

                    {inventory?.map((item, i) => (
                        <div key={item.id}>
                            <hr className={cn("my-1.5 border-gray-600/75", i === 0 ? "hidden" : "")} />
                            <div className="flex items-center justify-center gap-3 px-4">
                                <div className="w-[13%]">
                                    <img
                                        className={cn("z-10 mx-auto aspect-square max-w-10 grid-cols-2")}
                                        src={import.meta.env.VITE_IMAGE_CDN_URL + "/" + item.item.image}
                                        alt=""
                                        onError={(e) => (e.target.src = displayMissingIcon(true))}
                                    />
                                </div>
                                <div className="flex flex-1 flex-col">
                                    <p className={cn(rarityColours(item.item.rarity), "text-base")}>
                                        {item.item.name} {item.upgradeLevel > 0 && <span>+{item.upgradeLevel}</span>}
                                    </p>

                                    <div className="flex min-w-24 flex-col text-gray-200 text-sm">
                                        {item.item.damage > 0 && (
                                            <div>
                                                <span className="mr-1 text-blue-500 text-lg">
                                                    {getItemStatWithUpgrades(item, "damage")}
                                                </span>{" "}
                                                DMG
                                                <ItemComparisonArrow
                                                    item={item.item}
                                                    equippedItems={equippedItems}
                                                    type="damage"
                                                />
                                            </div>
                                        )}
                                        {item.item.armour > 0 && (
                                            <div className="">
                                                <span className="mr-1 text-blue-500 text-sm">
                                                    {getItemStatWithUpgrades(item, "armour")}
                                                </span>{" "}
                                                ARMOR
                                                <ItemComparisonArrow
                                                    item={item.item}
                                                    equippedItems={equippedItems}
                                                    type="armour"
                                                />
                                            </div>
                                        )}

                                        {item.item.baseAmmo > 0 ||
                                        item.item.strength > 0 ||
                                        item.item.dexterity > 0 ||
                                        item.item.statModifiers ? (
                                            <>
                                                {item.item.baseAmmo > 0 && (
                                                    <div className="text-base">
                                                        {item.item.baseAmmo} Base Ammo{" "}
                                                        <ItemComparisonArrow
                                                            item={item.item}
                                                            equippedItems={equippedItems}
                                                            type="ammo"
                                                        />
                                                    </div>
                                                )}
                                                {item.item.strength > 0 && (
                                                    <div className="text-base text-custom-yellow">
                                                        +{item.item.strength}% STR{" "}
                                                        <ItemComparisonArrow
                                                            item={item.item}
                                                            equippedItems={equippedItems}
                                                            type="strength"
                                                        />
                                                    </div>
                                                )}
                                                {item.item.dexterity > 0 && (
                                                    <div className="text-base text-custom-yellow">
                                                        +{item.item.dexterity}% DEX{" "}
                                                        <ItemComparisonArrow
                                                            item={item.item}
                                                            equippedItems={equippedItems}
                                                            type="dexterity"
                                                        />
                                                    </div>
                                                )}
                                                {item.item.statModifiers && (
                                                    <div className="mx-auto flex text-base text-custom-yellow">
                                                        {displayStats(item)}
                                                        <ItemComparisonArrow
                                                            item={item.item}
                                                            equippedItems={equippedItems}
                                                            type="modifiers"
                                                        />
                                                    </div>
                                                )}
                                            </>
                                        ) : null}
                                    </div>
                                </div>
                                <div className="-mr-2 flex flex-1 justify-end md:mr-0">
                                    {!getIsEquipped(item.item) ? (
                                        <Button onClick={() => equipItem.mutate({ currentUser, userItem: item })}>
                                            Equip
                                        </Button>
                                    ) : (
                                        <span className="mr-2 text-custom-yellow">Equipped</span>
                                    )}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        />
    );
};

export default EquipTooltip;
